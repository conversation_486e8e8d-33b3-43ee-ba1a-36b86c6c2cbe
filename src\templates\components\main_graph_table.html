{% include "main_background.html" %}
<!-- 图示交互视图组件 -->

<!-- 切换视图样式 -->
<style>
/* 图示视图容器显示控制 */
#main_graph_table_container.hidden {
    display: none !important;
}


/* 图示视图容器 */
.main_graph_table_container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-color: transparent;
    z-index: 1000;
    padding: 20px;
    overflow-y: auto;
    overflow-x: hidden; /* 隐藏水平滚动条 */
    display: block;
    scrollbar-width: thin;
    scrollbar-color: #2989d8 #f0f0f0;
}

.main_graph_table_container::-webkit-scrollbar {
    width: 10px;
}

.main_graph_table_container::-webkit-scrollbar-track {
    background: #f0f0f0;
    border-radius: 10px;
}

.main_graph_table_container::-webkit-scrollbar-thumb {
    background: #2989d8;
    border-radius: 10px;
}

.main_graph_table_container::-webkit-scrollbar-thumb:hover {
    background: #1e5799;
}

/* 图示标题栏 */
.main_graph_table_header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px 20px;
    background: linear-gradient(135deg, #1e5799, #2989d8);
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 10;
}

.main_graph_table_title {
    font-size: 24px;
    font-weight: 600;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.main_graph_table_refresh {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.main_graph_table_refresh:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 气泡图主体 */
.main_graph_table_graph_container {
    display: flex;
    gap: 25px; /* 稍微增加间距 */
    min-height: 250px; /* 增加最小高度 */
    overflow-x: auto;
    overflow-y: hidden; /* 隐藏竖向滚动条 */
    margin-bottom: 0;
    background: linear-gradient(180deg, #f8fbff, #eef5fe); /* 更柔和的浅蓝渐变 */
    scrollbar-width: thin; /* 使用细滚动条 */
    scrollbar-color: #79b6ff #eaf2f8; /* 滚动条颜色调整 */
    border-radius: 10px; /* 为容器本身添加圆角 */
    padding: 15px; /* 添加内边距 */
}

/* 自定义滚动条样式 (保持与上面一致) */
.main_graph_table_graph_container::-webkit-scrollbar {
    height: 10px; /* 略微加粗 */
}

.main_graph_table_graph_container::-webkit-scrollbar-track {
    background: #eaf2f8; /* 轨道颜色 */
    border-radius: 10px;
}

.main_graph_table_graph_container::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #79b6ff, #5aaaff); /* 渐变滑块 */
    border-radius: 10px;
    border: 2px solid #eaf2f8; /* 添加边框使其更突出 */
}

.main_graph_table_graph_container::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #5aaaff, #4a9eff); /* 悬停时更亮 */
}

/* 气泡列 */
.main_graph_table_column {
    flex: 0 0 auto;
    width: 260px; /* 略微加宽 */
    border-radius: 16px; /* 更大的圆角 */
    padding: 20px;
    background-color: rgb(144 226 231 / 11%); /* 轻微透明的白色背景 */
    backdrop-filter: blur(10px); /* 毛玻璃效果 */
    box-shadow: 0 8px 25px rgba(100, 150, 240, 0.12); /* 更柔和且分散的阴影 */
    display: flex;
    flex-direction: column;
    gap: 18px;
    transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
    border: 1px solid rgba(170, 200, 255, 0.3); /* 淡蓝色边框 */
    position: relative; /* 为可能的伪元素动画做准备 */
    overflow: hidden; /* 配合内部发光效果 */
}

.main_graph_table_column::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 16px;
    padding: 1px; /* 控制辉光的粗细 */
    background: linear-gradient(45deg, rgba(0, 123, 255, 0), rgba(0, 123, 255, 0), rgba(0, 191, 255, 0.3), rgba(0, 123, 255, 0), rgba(0, 123, 255, 0));
    background-size: 400% 400%;
    animation: columnShine 8s linear infinite;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.main_graph_table_column:hover::before {
    opacity: 1;
}

@keyframes columnShine {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.main_graph_table_column:hover {
    transform: translateY(-6px) scale(1.03);
    box-shadow: 0 12px 30px rgba(100, 150, 240, 0.2); /* 悬停时阴影加强 */
    border-color: rgba(135, 206, 250, 0.7);
}

.main_graph_table_column_title {
    text-align: center;
    background: linear-gradient(135deg, #3b82f6, #1e90ff, #25c6fc); /* 更现代的蓝色渐变 */
    -webkit-background-clip: text;
    color: transparent;
    font-size: 22px; /* 略微增大字号 */
    font-weight: 700; /* 加粗 */
    margin-bottom: 18px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(106, 90, 205, 0.15); /* 更细的下边框 */
    position: relative;
    letter-spacing: 0.5px; /* 轻微字间距 */
    text-shadow: 0 0 10px rgba(60, 130, 246, 0.2); /* 文字的轻微辉光 */
}

.main_graph_table_column_title:after {
    content: '';
    position: absolute;
    left: 50%;
    bottom: -2px;
    width: 70px; /* 略微加长 */
    height: 3px; /* 略微加粗 */
    background: linear-gradient(90deg, #6a11cb, #2575fc, #00d4ff); /* 更鲜艳的渐变 */
    transform: translateX(-50%);
    border-radius: 3px;
    animation: titleLinePulse 2.5s infinite ease-in-out;
}

@keyframes titleLinePulse {
    0% { opacity: 0.7; width: 70px; }
    50% { opacity: 1; width: 80px; background: linear-gradient(90deg, #8a41eb, #4595fc, #25e0ff); }
    100% { opacity: 0.7; width: 70px; }
}

/* 气泡组容器 */
.main_graph_table_bubble_group {
    background-color: rgba(248, 251, 255, 0.9); /* 更淡的背景，轻微透明 */
    border-radius: 12px; /* 统一圆角 */
    padding: 18px;
    margin-bottom: 15px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04); /* 更细微的阴影 */
    transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
    border: 1px solid rgba(210, 225, 250, 0.7); /* 淡紫色边框 */
    position: relative;
}

.main_graph_table_bubble_group:hover {
    background-color: #ffffff; /* 悬停时纯白 */
    box-shadow: 0 7px 18px rgba(100, 149, 237, 0.12); /* 悬停阴影 */
    border-color: rgba(135, 206, 250, 0.6);
    transform: translateY(-3px); /* 轻微上浮 */
}

.main_graph_table_group_title {
    font-size: 17px; /* 略微增大 */
    font-weight: 600;
    color:rgb(255, 90, 145); /* 调整为更明亮的蓝紫色 */
    margin-bottom: 15px;
    text-align: center;
    padding: 10px 12px; /* 调整内边距 */
    border-radius: 30px; /* 更圆的胶囊形状 */
    background: linear-gradient(135deg, rgba(120, 160, 255, 0.15), rgba(150, 190, 255, 0.2)); /* 淡蓝渐变 */
    position: relative;
    overflow: hidden;
    text-shadow: 0 0 5px rgba(90, 121, 255, 0.1);
}

.main_graph_table_group_title::before {
    content: '';
    position: absolute;
    top: 0;
    left: -120%; /* 初始位置更靠左 */
    width: 80%; /* 闪光条宽度 */
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 200, 224, 0.5), transparent); /* 更亮的闪光 */
    animation: group_title_shimmer 3.5s infinite linear; /* 调整动画速度和方式 */
    transform: skewX(-25deg); /* 轻微倾斜增加动感 */
}

@keyframes group_title_shimmer {
    0% { left: -120%; }
    100% { left: 120%; } /* 确保完全扫过 */
}

/* 气泡容器 */
.main_graph_table_bubbles {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
}

/* 气泡样式 */
.main_graph_table_bubble {
    border-radius: 30px; /* 更圆的胶囊形状 */
    padding: 10px 20px; /* 调整内边距 */
    font-size: 14px;
    font-weight: 500; /* 字体稍粗 */
    background: linear-gradient(135deg, #e6f0ff, #f0f7ff); /* 非常浅的蓝白渐变 */
    color: #3a7bd5; /* 主题蓝色 */
    border: 1px solid rgba(173, 216, 230, 0.6); /* 淡青色边框 */
    cursor: pointer;
    transition: all 0.35s cubic-bezier(0.19, 1, 0.22, 1);
    position: relative;
    overflow: visible; /* 改为visible以显示外部辉光 */
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 100%;
    z-index: 1;
    box-shadow: 0 3px 8px rgba(176, 224, 230, 0.25); /* 更柔和的阴影 */
}

.main_graph_table_bubble::before { /* 用于创建悬停时的辉光背景 */
    content: '';
    position: absolute;
    top: -2px; left: -2px; right: -2px; bottom: -2px; /* 比气泡稍大 */
    border-radius: 32px; /* 对应气泡的圆角 */
    background: radial-gradient(circle at center, rgba(135, 206, 250, 0.4) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.35s ease;
    z-index: -1;
    transform: scale(0.95);
}

.main_graph_table_bubble:hover {
    transform: translateY(-4px) scale(1.06);
    box-shadow: 0 6px 18px rgba(135, 206, 250, 0.4); /* 悬停时更强的阴影 */
    border-color: rgba(120, 180, 250, 0.8); /* 边框颜色变化 */
    color: #1c6bcf; /* 悬停时颜色加深 */
    background: linear-gradient(135deg, #f0f7ff, #e6f0ff); /* 悬停时背景反转或微调 */
}

.main_graph_table_bubble:hover::before {
    opacity: 1;
    transform: scale(1);
    animation: subtleShine 1.5s infinite alternate;
}

@keyframes subtleShine { /* 用于气泡背景辉光的微妙动画 */
    from { opacity: 0.7; filter: brightness(100%); }
    to { opacity: 1; filter: brightness(120%); }
}

/* 选中气泡样式 */
.main_graph_table_bubble.active {
    background: linear-gradient(135deg, #5e9dff, #3b82f6); /* 更有活力的蓝色渐变 */
    color: white;
    border-color: #3b82f6;
    box-shadow: 0 0 15px rgba(60, 130, 246, 0.5), 0 0 25px rgba(60, 130, 246, 0.3); /* 明显的荧光效果 */
    transform: translateY(-5px) scale(1.08);
    font-weight: 600; /* 激活时字体更粗 */
    animation: activeBubblePulse 1.8s infinite ease-in-out;
}

.main_graph_table_bubble.active::before { /* 激活状态下的辉光 */
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.3) 0%, transparent 60%);
    opacity: 1;
    transform: scale(1.05);
    animation: active_bubble_glow 1.8s infinite alternate; /* 复用或调整现有动画 */
}

@keyframes activeBubblePulse { /* 为激活的气泡主体添加脉冲动画 */
    0% { box-shadow: 0 0 12px rgba(60, 130, 246, 0.4), 0 0 20px rgba(60, 130, 246, 0.25); transform: translateY(-5px) scale(1.08); }
    50% { box-shadow: 0 0 20px rgba(60, 130, 246, 0.6), 0 0 30px rgba(60, 130, 246, 0.4); transform: translateY(-6px) scale(1.1); }
    100% { box-shadow: 0 0 12px rgba(60, 130, 246, 0.4), 0 0 20px rgba(60, 130, 246, 0.25); transform: translateY(-5px) scale(1.08); }
}

/* 连接线效果 */
.main_graph_table_connection_line {
    position: absolute;
    background-color: rgba(52, 152, 219, 0.5);
    pointer-events: none;
    z-index: 100;
    border-radius: 2px;
    transition: opacity 0.3s ease;
    box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
}

/* 类别行容器 - 紧凑型设计 */
.main_graph_table_category_row {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;
    align-items: flex-start;
}

/* 简单的类别行布局 */

/* 类别区域 - 每个类别的独立区域 */
.main_graph_table_category_section {
    flex: 1;
    min-width: 0;
    display: flex;
    align-items: flex-start;
    gap: 12px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 8px;
    padding: 8px 12px;
    border: 1px solid rgba(226, 232, 240, 0.3);
    transition: all 0.2s ease;
}

.main_graph_table_category_section:hover {
    background: rgba(255, 255, 255, 0.8);
    border-color: rgba(59, 130, 246, 0.2);
}

/* 调整类别标签样式 - 紧凑设计 */
.main_graph_table_category_label {
    flex-shrink: 0;
    min-width: 80px;
    margin-bottom: 0;
}

/* 移除第一行类别标签的特殊样式 */

/* 移除第一行的类别区域特殊设置，保持一致布局 */

/* 调整组件容器样式 - 紧凑布局 */
.main_graph_table_components_container {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 6px;
    align-items: flex-start;
}

/* 移除第一行的特殊样式，让所有行保持一致 */

/* 紧凑型组件卡片样式 */
.main_graph_table_component_card {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(226, 232, 240, 0.5);
    border-radius: 6px;
    padding: 6px 8px;
    margin-bottom: 4px;
    transition: all 0.2s ease;
    font-size: 13px;
    line-height: 1.3;
}

.main_graph_table_component_card:hover {
    background: rgba(255, 255, 255, 0.95);
    border-color: rgba(59, 130, 246, 0.4);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}

/* 移除第一行组件卡片的特殊样式，保持一致 */

/* 紧凑型空状态消息样式 */
.main_graph_table_empty_message {
    background: rgba(248, 250, 252, 0.6);
    border: 1px dashed rgba(203, 213, 225, 0.6);
    border-radius: 6px;
    padding: 12px;
    text-align: center;
    color: #64748b !important;
    font-style: italic;
    font-size: 12px !important;
    line-height: 1.4;
}

/* 移除第一行空状态消息的特殊样式 */

/* 收回状态的样式 */
.main_graph_table_collapse_message {
    background: rgba(248, 250, 252, 0.8) !important;
    border: 1px dashed rgba(203, 213, 225, 0.8) !important;
    border-radius: 8px !important;
    padding: 20px !important;
    text-align: center !important;
    color: #64748b !important;
    font-style: italic !important;
    font-size: 14px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    overflow: hidden !important;
}

.main_graph_table_collapse_message:hover {
    background: rgba(239, 246, 255, 0.9) !important;
    border-color: rgba(59, 130, 246, 0.4) !important;
    color: #3b82f6 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15) !important;
}

.main_graph_table_collapse_message::before {
    content: '🔄' !important;
    margin-right: 8px !important;
    font-size: 16px !important;
}

/* 收回状态容器的样式 */
.main_graph_table_components_container.collapsed {
    min-height: 80px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* 收回到同一行的类别区域样式 - 美化版本 */
.main_graph_table_category_section.collapsed-to-row {
    flex: 0 0 auto !important;
    min-width: 140px !important;
    max-width: 180px !important;
    margin-right: 20px !important;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9)) !important;
    border: 2px solid rgba(59, 130, 246, 0.2) !important;
    border-radius: 16px !important;
    padding: 12px 16px !important;
    transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1) !important;
    cursor: pointer !important;
    position: relative !important;
    overflow: hidden !important;
    box-shadow:
        0 4px 20px rgba(59, 130, 246, 0.08),
        0 2px 8px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
}

.main_graph_table_category_section.collapsed-to-row::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(147, 197, 253, 0.1)) !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
    z-index: -1 !important;
}

.main_graph_table_category_section.collapsed-to-row:hover::before {
    opacity: 1 !important;
}

.main_graph_table_category_section.collapsed-to-row:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 1), rgba(239, 246, 255, 0.95)) !important;
    border-color: rgba(59, 130, 246, 0.4) !important;
    transform: translateY(-4px) scale(1.05) !important;
    box-shadow:
        0 8px 30px rgba(59, 130, 246, 0.15),
        0 4px 16px rgba(0, 0, 0, 0.08),
        inset 0 2px 0 rgba(255, 255, 255, 0.9) !important;
}

.main_graph_table_category_section.collapsed-to-row .main_graph_table_category_label {
    margin-bottom: 0 !important;
    text-align: center !important;
    flex-direction: column !important;
    gap: 8px !important;
}

.main_graph_table_category_section.collapsed-to-row .main_graph_table_category_icon {
    width: 40px !important;
    height: 40px !important;
    font-size: 18px !important;
    margin-bottom: 0 !important;
    transition: all 0.3s ease !important;
}

.main_graph_table_category_section.collapsed-to-row:hover .main_graph_table_category_icon {
    transform: scale(1.1) rotate(5deg) !important;
}

.main_graph_table_category_section.collapsed-to-row .main_graph_table_category_text {
    font-size: 12px !important;
    font-weight: 700 !important;
    color: #1e40af !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    letter-spacing: 0.3px !important;
}

/* 添加收回动画效果 */
@keyframes collapseToRow {
    from {
        opacity: 1;
        transform: scale(1) translateX(0);
        max-width: 100%;
    }
    to {
        opacity: 1;
        transform: scale(0.85) translateX(10px);
        max-width: 180px;
    }
}

@keyframes expandFromRow {
    from {
        opacity: 1;
        transform: scale(0.85) translateX(10px);
        max-width: 180px;
    }
    to {
        opacity: 1;
        transform: scale(1) translateX(0);
        max-width: 100%;
    }
}

.main_graph_table_category_section.collapsed-to-row {
    animation: collapseToRow 0.5s cubic-bezier(0.19, 1, 0.22, 1) forwards !important;
}

/* 为整体布局添加更好的视觉层次 */
.main_graph_table_category_row {
    position: relative !important;
}

.main_graph_table_category_row::before {
    content: '' !important;
    position: absolute !important;
    top: -5px !important;
    left: -10px !important;
    right: -10px !important;
    bottom: -5px !important;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.02), rgba(147, 197, 253, 0.05)) !important;
    border-radius: 12px !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
    z-index: -1 !important;
}

.main_graph_table_category_row:hover::before {
    opacity: 1 !important;
}

/* 移除智能收缩相关样式 */

/* 当切换到表格视图时隐藏图示视图 */
.main_graph_table_container.hidden {
    display: none;
}

/* 当处于图示视图时隐藏表格视图 */
.dynamic_table_view.hidden {
    display: none;
}

/* 波纹效果 */
.main_graph_table_ripple {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.7);
    transform: scale(0);
    animation: main_graph_table_ripple 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
    pointer-events: none;
}

@keyframes main_graph_table_ripple {
    to {
        transform: scale(2.5);
        opacity: 0;
    }
}

/* 提示信息 */
.main_graph_table_tooltip {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 15px;
    border-radius: 8px;
    font-size: 13px;
    z-index: 1000;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    white-space: nowrap;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* 加载动画 */
.main_graph_table_loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.main_graph_table_loading.active {
    opacity: 1;
    pointer-events: auto;
}

.main_graph_table_spinner {
    width: 60px;
    height: 60px;
    border: 6px solid rgba(41, 128, 185, 0.2);
    border-top-color: #2980b9;
    border-radius: 50%;
    animation: main_graph_table_spin 1s linear infinite;
}

@keyframes main_graph_table_spin {
    to {
        transform: rotate(360deg);
    }
}

/* 思维导图样式 */
.main_graph_table_mindmap_container {
    width: 92%;
    max-width: 1600px;
    margin: 0 auto 50px auto;
    background-color: #d4ebfc;
    border-radius: 16px;
    padding: 0;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
    max-height: 0;
    position: relative;
    overflow: hidden; /* 防止内容溢出 */
    border: 1px solid rgba(41, 128, 185, 0.1);
}

.main_graph_table_mindmap_container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(100, 149, 237, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(106, 90, 205, 0.05) 0%, transparent 40%);
    z-index: 0;
    pointer-events: none;
}

.main_graph_table_mindmap_container.active {
    animation: mindmap-fade-in 0.8s cubic-bezier(0.19, 1, 0.22, 1);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
    border: 1px solid rgba(41, 128, 185, 0.15);
    opacity: 1;
    transform: translateY(0);
    padding: 0 0 20px 0;
    max-height: 2500px; /* 增加最大高度以适应更多内容 */
    margin-top: 20px;
}

@keyframes mindmap-fade-in {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 思维导图头部增强样式 */
.main_graph_table_mindmap_header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    background: linear-gradient(135deg, #f5f9ff, #e0edff);
    border-bottom: 1px solid rgba(41, 128, 185, 0.1);
    position: relative;
    overflow: hidden;
    z-index: 20;
}

.main_graph_table_mindmap_header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
        transparent,
        rgba(66, 133, 244, 0.3),
        rgba(219, 68, 55, 0.3),
        rgba(244, 180, 0, 0.3),
        rgba(15, 157, 88, 0.3),
        transparent);
}

.main_graph_table_mindmap_title {
    font-size: 22px;
    font-weight: 600;
    color: transparent;
    margin: 0;
    background: linear-gradient(135deg, #3a7bd5, #00d2ff);
    -webkit-background-clip: text;
    position: relative;
    padding-left: 50px;
}

.main_graph_table_mindmap_title::before {
    content: '\f0e8';
    font-family: 'Font Awesome 5 Free';
    position: absolute;
    left: 0;
    top: 0%;
    transform: translateY(-50%);
    font-size: 24px;
    color: #3a7bd5;
    animation: icon_pulse 2s infinite;
}

.main_graph_table_mindmap_controls {
    display: flex;
    gap: 12px;
}

.main_graph_table_close_mindmap,
.main_graph_table_reset_mindmap {
    background: linear-gradient(135deg, #3a7bd5, #00d2ff);
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(58, 123, 213, 0.2);
}

/* 控制按钮悬浮增强效果 */
.main_graph_table_close_mindmap:hover,
.main_graph_table_reset_mindmap:hover {
    transform: scale(1.15) rotate(360deg);
    box-shadow: 0 7px 20px rgba(58, 123, 213, 0.4);
}

.main_graph_table_close_mindmap:active,
.main_graph_table_reset_mindmap:active {
    transform: scale(0.95);
    box-shadow: 0 3px 10px rgba(58, 123, 213, 0.3);
}

/* 在思维导图下添加描述 */
.main_graph_table_mindmap_container.active::after {
    content: '业务支持组件组合关系可视化交互展示';
    position: absolute;
    top: 66px;
    left: 0;
    right: 0;
    padding: 15px 25px;
    color: #6c757d;
    font-size: 15px;
    font-style: italic;
    background: rgba(240, 248, 255, 0.6);
    border-bottom: 1px dashed rgba(41, 128, 185, 0.2);
    z-index: 10;
}

/* 修改缩放控制按钮样式适配新风格 */
.main_graph_table_mindmap_zoom_controls {
    position: absolute;
    right: 20px;
    bottom: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 100;
}

.main_graph_table_zoom_btn {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    background: linear-gradient(135deg, #f5f9ff, #e0edff);
    border: 1px solid rgba(41, 128, 185, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 18px;
    color: #3a7bd5;
    box-shadow: 0 4px 15px rgba(58, 123, 213, 0.2);
    transition: all 0.3s ease;
}

/* ECharts容器增强 */
.main_graph_table_echarts_container {
    width: 100%;
    height: 100%;
    transition: all 0.5s ease;
    background: transparent;
    position: relative;
    overflow: hidden;
}

/* 专区树状图样式 */
.main_graph_table_tree_showcase {
    zoom:115%;%
    width: 80%;
    max-width: 1200px;
    margin: 20px auto;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    overflow: hidden;
    position: relative;
    border: 1px solid rgba(41, 128, 185, 0.2);
    transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
    transform: translateY(20px);
    opacity: 0;
    pointer-events: none;
}

.main_graph_table_tree_showcase.active {
    transform: translateY(0);
    opacity: 1;
    pointer-events: all;
    animation: tree-showcase-appear 0.8s cubic-bezier(0.19, 1, 0.22, 1) forwards;
}

@keyframes tree-showcase-appear {
    0% {
        transform: translateY(20px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

.main_graph_table_tree_showcase::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 70%;
    height: 10px;
    border-radius: 50%;
    box-shadow: 0 0 20px 5px rgba(0, 0, 0, 0.1);
    z-index: -1;
}

.main_graph_table_tree_header:hover .main_graph_table_tree_title::before {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        opacity: 0.6;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0.6;
    }
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .main_graph_table_tree_showcase {
        width: 90%;
    }
}

@media (max-width: 768px) {
    .main_graph_table_tree_showcase {
        width: 95%;
    }

    .main_graph_table_mindmap {
        min-height: 400px;
        height: 400px;
    }

    .main_graph_table_tree_legend {
        gap: 15px;
    }
}

.main_graph_table_tree_showcase::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
                rgba(41, 128, 185, 0.03) 0%,
                transparent 50%,
                rgba(41, 128, 185, 0.02) 100%);
    pointer-events: none;
}

.main_graph_table_tree_header {
    padding: 15px 20px;
    background: linear-gradient(90deg, #f5f9ff, #e8f4ff);
    border-bottom: 1px solid rgba(41, 128, 185, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.main_graph_table_tree_title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    position: relative;
    padding-left: 15px;
}

.main_graph_table_tree_title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 18px;
    background: linear-gradient(to bottom, #3498db, #2980b9);
    border-radius: 3px;
}

.main_graph_table_tree_info {
    font-size: 14px;
    color: #7f8c8d;
    font-style: italic;
}

.main_graph_table_tree_content {
    padding: 0;
    background: #f9fbff;
    border-radius: 0 0 8px 8px;
    position: relative;
}

.main_graph_table_mindmap {
    position: relative;
    min-height: 500px;
    height: 500px;
    width: 100%;
    padding: 10px;
    overflow: hidden;
    background: radial-gradient(circle at center, #f8f9ff 0%, #eaeeff 100%);
    transition: all 0.3s ease;
}

.main_graph_table_tree_footer {
    padding: 10px 20px;
    background: #f5f9ff;
    border-top: 1px solid rgba(41, 128, 185, 0.1);
}

.main_graph_table_tree_legend {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.main_graph_table_legend_item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.main_graph_table_legend_icon {
    width: 16px;
    height: 16px;
    border-radius: 3px;
    display: inline-block;
}

.main_graph_table_legend_icon.root {
    background: linear-gradient(135deg, #ff6e76, #ec83b6);
}

.main_graph_table_legend_icon.type {
    background: linear-gradient(135deg, #e19d0e, #ecca83);
}

.main_graph_table_legend_icon.leaf {
    background: #26a6f2;
}

.main_graph_table_legend_text {
    font-size: 14px;
    color: #34495e;
}

/* 节点点击波纹特效 */
.node-click-ripple {
    position: absolute;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.7);
    transform: translate(-50%, -50%);
    pointer-events: none;
    z-index: 100;
    animation: node-ripple 1s cubic-bezier(0, 0.2, 0.8, 1) forwards;
}

@keyframes node-ripple {
    0% {
        width: 0;
        height: 0;
        opacity: 0.8;
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.3);
    }
    100% {
        width: 200px;
        height: 200px;
        opacity: 0;
        box-shadow: 0 0 0 20px rgba(255, 255, 255, 0);
    }
}

/* 添加节点连接线动画 */
@keyframes edge-glow {
    0% {
        opacity: 0.3;
        stroke-dashoffset: 1000;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0.3;
        stroke-dashoffset: 0;
    }
}

/* 响应式样式 */
@media (max-width: 992px) {
    .main_graph_table_column {
        width: 200px;
    }


}

/* 隐藏左侧面板切换按钮 */
.main_graph_table_container .panel-toggle,
.main_graph_table_container ~ .panel-toggle {
    display: none !important;
}

.main_graph_table_scroll_hint {
    position: fixed;
    bottom: 100px;
    right: 30px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 15px;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    z-index: 1100;
    animation: main_graph_table_bounce 2s infinite;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    transition: opacity 0.5s ease;
}

.main_graph_table_scroll_hint.fade-out {
    opacity: 0;
}

.main_graph_table_scroll_arrow {
    font-size: 24px;
    animation: main_graph_table_arrow_bounce 1.5s infinite;
}

.main_graph_table_scroll_text {
    font-size: 14px;
    font-weight: 500;
}

@keyframes main_graph_table_bounce {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes main_graph_table_arrow_bounce {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(5px);
    }
}

.bubble_showcase {
    width: 92%;
    max-width: 1600px;
    margin: 0 auto 40px auto;
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    position: relative;
    border: 1px solid rgba(41, 128, 185, 0.1);
    transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
}

.bubble_showcase::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(100, 149, 237, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(106, 90, 205, 0.05) 0%, transparent 40%);
    pointer-events: none;
}

.bubble_showcase_header {
    padding: 20px 25px;
    background: linear-gradient(135deg, #f5f9ff, #e0edff);
    border-bottom: 1px solid rgba(41, 128, 185, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.bubble_showcase_header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
        transparent,
        rgba(66, 133, 244, 0.3),
        rgba(219, 68, 55, 0.3),
        rgba(244, 180, 0, 0.3),
        rgba(15, 157, 88, 0.3),
        transparent);
}

.bubble_showcase_title {
    display: flex;
    align-items: center;
    gap: 15px;
}

.bubble_showcase_title h2 {
    margin: 0;
    font-size: 22px;
    font-weight: 600;
    background: linear-gradient(135deg, #3a7bd5, #00d2ff);
    -webkit-background-clip: text;
    color: transparent;
}

.bubble_icon_pulse {
    font-size: 24px;
    color: #3a7bd5;
    animation: icon_pulse 2s infinite;
}

@keyframes icon_pulse {
    0% {
        opacity: 0.6;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.1);
    }
    100% {
        opacity: 0.6;
        transform: scale(1);
    }
}

.main_graph_table_refresh {
    background: linear-gradient(135deg, #3a7bd5, #00d2ff);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 30px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(58, 123, 213, 0.3);
}

.main_graph_table_refresh:hover {
    transform: translateY(-3px);
    box-shadow: 0 7px 20px rgba(58, 123, 213, 0.4);
}

.main_graph_table_refresh:active {
    transform: translateY(1px);
    box-shadow: 0 3px 10px rgba(58, 123, 213, 0.3);
}

.refresh_icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.main_graph_table_refresh:hover .refresh_icon {
    animation: spin 1s ease;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.bubble_description {
    padding: 15px 25px;
    color: #6c757d;
    font-size: 15px;
    font-style: italic;
    background: rgba(240, 248, 255, 0.6);
    border-bottom: 1px dashed rgba(41, 128, 185, 0.2);
}

/* 修改气泡容器样式 */
.main_graph_table_graph_container {
    display: flex;
    gap: 25px; /* 稍微增加间距 */
    min-height: 250px; /* 增加最小高度 */
    overflow-x: auto;
   /* 调整内边距 */
    margin-bottom: 0;
    background: linear-gradient(180deg, #f8fbff, #eef5fe); /* 更柔和的浅蓝渐变 */
    scrollbar-width: revert;
    scrollbar-color: #79b6ff #eaf2f8; /* 滚动条颜色调整 */
    border-radius: 10px; /* 为容器本身添加圆角 */
}

/* 自定义滚动条样式 (保持与上面一致) */
.main_graph_table_graph_container::-webkit-scrollbar {
    height: 10px; /* 略微加粗 */
}

.main_graph_table_graph_container::-webkit-scrollbar-track {
    background: #eaf2f8; /* 轨道颜色 */
    border-radius: 10px;
}

.main_graph_table_graph_container::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #79b6ff, #5aaaff); /* 渐变滑块 */
    border-radius: 10px;
    border: 2px solid #eaf2f8; /* 添加边框使其更突出 */
}

.main_graph_table_graph_container::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #5aaaff, #4a9eff); /* 悬停时更亮 */
}

/* 修改列样式 */
.main_graph_table_column {
    flex: 0 0 auto;
    width: 260px; /* 略微加宽 */
    border-radius: 16px; /* 更大的圆角 */
    padding: 20px;
    background-color:  rgb(144 226 231 / 11%); /* 轻微透明的白色背景 */
    backdrop-filter: blur(10px); /* 毛玻璃效果 */
    box-shadow: 0 8px 25px rgba(100, 150, 240, 0.12); /* 更柔和且分散的阴影 */
    display: flex;
    flex-direction: column;
    gap: 18px;
    transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
    border: 1px solid rgba(170, 200, 255, 0.3); /* 淡蓝色边框 */
    position: relative; /* 为可能的伪元素动画做准备 */
    overflow: hidden; /* 配合内部发光效果 */
}

.main_graph_table_column::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 16px;
    padding: 1px; /* 控制辉光的粗细 */
    background: linear-gradient(45deg, rgba(0, 123, 255, 0), rgba(0, 123, 255, 0), rgba(0, 191, 255, 0.3), rgba(0, 123, 255, 0), rgba(0, 123, 255, 0));
    background-size: 400% 400%;
    animation: columnShine 8s linear infinite;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.main_graph_table_column:hover::before {
    opacity: 1;
}

@keyframes columnShine {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.main_graph_table_column:hover {
    transform: translateY(-6px) scale(1.03);
    box-shadow: 0 12px 30px rgba(100, 150, 240, 0.2); /* 悬停时阴影加强 */
    border-color: rgba(135, 206, 250, 0.7);
}

.main_graph_table_column_title {
    text-align: center;
    background: linear-gradient(135deg, #3b82f6, #1e90ff, #25c6fc); /* 更现代的蓝色渐变 */
    -webkit-background-clip: text;
    color: transparent;
    font-size: 25px; /* 略微增大字号 */
    font-weight: 700; /* 加粗 */
    margin-bottom: 18px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(106, 90, 205, 0.15); /* 更细的下边框 */
    position: relative;
    letter-spacing: 0.5px; /* 轻微字间距 */
    text-shadow: 0 0 10px rgba(60, 130, 246, 0.2); /* 文字的轻微辉光 */
}

.main_graph_table_column_title:after {
    content: '';
    position: absolute;
    left: 50%;
    bottom: -2px;
    width: 70px; /* 略微加长 */
    height: 3px; /* 略微加粗 */
    background: linear-gradient(90deg, #6a11cb, #2575fc, #00d4ff); /* 更鲜艳的渐变 */
    transform: translateX(-50%);
    border-radius: 3px;
    animation: titleLinePulse 2.5s infinite ease-in-out;
}

@keyframes titleLinePulse {
    0% { opacity: 0.9; width: 180px; }
    50% { opacity: 1; width: 230px; background: linear-gradient(90deg, #8a41eb, #4595fc, #25e0ff); }
    100% { opacity: 0.9; width: 180px; }
}

/* 修改气泡组样式 */
.main_graph_table_bubble_group {
    background-color: rgba(248, 251, 255, 0.9); /* 更淡的背景，轻微透明 */
    border-radius: 12px; /* 统一圆角 */
    padding: 18px;
    margin-bottom: 15px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04); /* 更细微的阴影 */
    transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
    border: 1px solid rgba(210, 225, 250, 0.7); /* 淡紫色边框 */
    position: relative;
}

.main_graph_table_bubble_group:hover {
    background-color: #ffffff; /* 悬停时纯白 */
    box-shadow: 0 7px 18px rgba(100, 149, 237, 0.12); /* 悬停阴影 */
    border-color: rgba(135, 206, 250, 0.6);
    transform: translateY(-3px); /* 轻微上浮 */
}

.main_graph_table_group_title {
    font-size: 20px; /* 略微增大 */
    font-weight: 600;
    color:rgb(242, 108, 133); /* 调整为更明亮的蓝紫色 */
    margin-bottom: 15px;
    text-align: center;
    padding: 10px 12px; /* 调整内边距 */
    border-radius: 30px; /* 更圆的胶囊形状 */
    background: linear-gradient(135deg, rgba(255, 120, 251, 0.15), rgba(255, 150, 211, 0.2)); /* 淡蓝渐变 */
    position: relative;
    overflow: hidden;
    text-shadow: 0 0 5px rgba(90, 121, 255, 0.1);
}

.main_graph_table_group_title::before {
    content: '';
    position: absolute;
    top: 0;
    left: -120%; /* 初始位置更靠左 */
    width: 80%; /* 闪光条宽度 */
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 200, 250, 0.5), transparent); /* 更亮的闪光 */
    animation: group_title_shimmer 3.5s infinite linear; /* 调整动画速度和方式 */
    transform: skewX(-25deg); /* 轻微倾斜增加动感 */
}

@keyframes group_title_shimmer {
    0% { left: -120%; }
    100% { left: 120%; } /* 确保完全扫过 */
}

/* 修改气泡样式 */
.main_graph_table_bubble {
    border-radius: 30px; /* 更圆的胶囊形状 */
    padding: 10px 20px; /* 调整内边距 */
    font-size: 14px;
    font-weight: 500; /* 字体稍粗 */
    background: linear-gradient(135deg, #e6f0ff, #f0f7ff); /* 非常浅的蓝白渐变 */
    color: #3a7bd5; /* 主题蓝色 */
    border: 1px solid rgba(173, 216, 230, 0.6); /* 淡青色边框 */
    cursor: pointer;
    transition: all 0.35s cubic-bezier(0.19, 1, 0.22, 1);
    position: relative;
    overflow: visible; /* 改为visible以显示外部辉光 */
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 100%;
    z-index: 1;
    box-shadow: 0 3px 8px rgba(176, 224, 230, 0.25); /* 更柔和的阴影 */
}

.main_graph_table_bubble::before { /* 用于创建悬停时的辉光背景 */
    content: '';
    position: absolute;
    top: -2px; left: -2px; right: -2px; bottom: -2px; /* 比气泡稍大 */
    border-radius: 32px; /* 对应气泡的圆角 */
    background: radial-gradient(circle at center, rgba(135, 206, 250, 0.4) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.35s ease;
    z-index: -1;
    transform: scale(0.95);
}

.main_graph_table_bubble:hover {
    transform: translateY(-4px) scale(1.06);
    box-shadow: 0 6px 18px rgba(135, 206, 250, 0.4); /* 悬停时更强的阴影 */
    border-color: rgba(120, 180, 250, 0.8); /* 边框颜色变化 */
    color: #1c6bcf; /* 悬停时颜色加深 */
    background: linear-gradient(135deg, #f0f7ff, #e6f0ff); /* 悬停时背景反转或微调 */
}

.main_graph_table_bubble:hover::before {
    opacity: 1;
    transform: scale(1);
    animation: subtleShine 1.5s infinite alternate;
}

@keyframes subtleShine { /* 用于气泡背景辉光的微妙动画 */
    from { opacity: 0.7; filter: brightness(100%); }
    to { opacity: 1; filter: brightness(120%); }
}

/* 选中气泡样式 */
.main_graph_table_bubble.active {
    background: linear-gradient(135deg, #5e9dff, #3b82f6); /* 更有活力的蓝色渐变 */
    color: white;
    border-color: #3b82f6;
    box-shadow: 0 0 15px rgba(60, 130, 246, 0.5), 0 0 25px rgba(60, 130, 246, 0.3); /* 明显的荧光效果 */
    transform: translateY(-5px) scale(1.08);
    font-weight: 600; /* 激活时字体更粗 */
    animation: activeBubblePulse 1.8s infinite ease-in-out;
}

.main_graph_table_bubble.active::before { /* 激活状态下的辉光 */
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.3) 0%, transparent 60%);
    opacity: 1;
    transform: scale(1.05);
    animation: active_bubble_glow 1.8s infinite alternate; /* 复用或调整现有动画 */
}

@keyframes activeBubblePulse { /* 为激活的气泡主体添加脉冲动画 */
    0% { box-shadow: 0 0 12px rgba(60, 130, 246, 0.4), 0 0 20px rgba(60, 130, 246, 0.25); transform: translateY(-5px) scale(1.08); }
    50% { box-shadow: 0 0 20px rgba(60, 130, 246, 0.6), 0 0 30px rgba(60, 130, 246, 0.4); transform: translateY(-6px) scale(1.1); }
    100% { box-shadow: 0 0 12px rgba(60, 130, 246, 0.4), 0 0 20px rgba(60, 130, 246, 0.25); transform: translateY(-5px) scale(1.08); }
}

/* 添加媒体查询以保持响应式设计 */
@media (max-width: 992px) {
    .bubble_showcase {
        width: 95%;
    }
}

/* 新的分类组件布局样式 - 左侧图标板 + 右侧内容区域 */
.main_graph_table_category_container {
    display: flex;
    flex-direction: row;
    gap: 20px;
    padding: 35px;
    background:
        linear-gradient(135deg,
            rgba(59, 130, 246, 0.08) 0%,
            rgba(37, 99, 235, 0.06) 25%,
            rgba(29, 78, 216, 0.04) 50%,
            rgba(37, 99, 235, 0.06) 75%,
            rgba(59, 130, 246, 0.08) 100%
        ),
        radial-gradient(ellipse at top left, rgba(96, 165, 250, 0.05) 0%, transparent 65%),
        radial-gradient(ellipse at bottom right, rgba(147, 197, 253, 0.04) 0%, transparent 65%),
        radial-gradient(ellipse at center, rgba(219, 234, 254, 0.03) 0%, transparent 70%);
    border-radius: 20px;
    min-height: 450px;
    position: relative;
    overflow: hidden;
    border: 2px solid rgba(59, 130, 246, 0.25);
    background-clip: padding-box;
    box-shadow:
        0 12px 40px rgba(59, 130, 246, 0.15),
        0 6px 20px rgba(37, 99, 235, 0.08),
        0 2px 8px rgba(0, 0, 0, 0.04),
        inset 0 2px 0 rgba(255, 255, 255, 0.4),
        inset 0 -1px 0 rgba(59, 130, 246, 0.1);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
}

.main_graph_table_category_container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(135deg,
            rgba(59, 130, 246, 0.04) 0%,
            transparent 30%,
            rgba(37, 99, 235, 0.02) 50%,
            transparent 70%,
            rgba(59, 130, 246, 0.03) 100%
        ),
        conic-gradient(from 45deg at 20% 20%,
            rgba(96, 165, 250, 0.02) 0deg,
            transparent 90deg,
            rgba(147, 197, 253, 0.015) 180deg,
            transparent 270deg
        );
    pointer-events: none;
    animation: subtleShimmer 8s ease-in-out infinite;
}

@keyframes subtleShimmer {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

/* 左侧竖向图标显示板样式 */
.main_graph_table_icon_panel {
    flex: 0 0 120px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 20px 10px;
    background:
        linear-gradient(135deg,
            rgba(255, 255, 255, 0.9) 0%,
            rgba(248, 250, 252, 0.85) 50%,
            rgba(255, 255, 255, 0.9) 100%
        );
    border-radius: 16px;
    border: 2px solid rgba(147, 197, 253, 0.3);
    box-shadow:
        0 8px 25px rgba(59, 130, 246, 0.12),
        0 3px 10px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    position: sticky;
    top: 20px;
    align-self: flex-start;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.main_graph_table_icon_panel::-webkit-scrollbar {
    display: none;
}

/* 图标按钮样式 */
.main_graph_table_icon_button {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 15px 10px;
    border: none;
    background: transparent;
    cursor: pointer;
    border-radius: 12px;
    transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
    position: relative;
    overflow: hidden;
}

.main_graph_table_icon_button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 197, 253, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 12px;
}

.main_graph_table_icon_button:hover::before {
    opacity: 1;
}

.main_graph_table_icon_button:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.15);
}

/* 图标容器 */
.main_graph_table_panel_icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    position: relative;
    transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* 图标文字标签 */
.main_graph_table_panel_text {
    font-size: 11px;
    font-weight: 600;
    color: #64748b;
    text-align: center;
    letter-spacing: 0.2px;
    line-height: 1.2;
    transition: color 0.3s ease;
}

/* 不同类别的图标颜色 */
.main_graph_table_panel_icon.middleware {
    background: linear-gradient(135deg, #3b82f6, #1e90ff, #25c6fc);
    box-shadow: 0 3px 12px rgba(59, 130, 246, 0.25);
}

.main_graph_table_panel_icon.database {
    background: linear-gradient(135deg, #f59e0b, #f97316, #fb923c);
    box-shadow: 0 3px 12px rgba(245, 158, 11, 0.25);
}

.main_graph_table_panel_icon.container {
    background: linear-gradient(135deg, #8b5cf6, #a855f7, #c084fc);
    box-shadow: 0 3px 12px rgba(139, 92, 246, 0.25);
}

.main_graph_table_panel_icon.backup {
    background: linear-gradient(135deg, #06b6d4, #0891b2, #0e7490);
    box-shadow: 0 3px 12px rgba(6, 182, 212, 0.25);
}

.main_graph_table_panel_icon.os {
    background: linear-gradient(135deg, #10b981, #059669, #047857);
    box-shadow: 0 3px 12px rgba(16, 185, 129, 0.25);
}

.main_graph_table_panel_icon.cpu {
    background: linear-gradient(135deg, #ef4444, #dc2626, #b91c1c);
    box-shadow: 0 3px 12px rgba(239, 68, 68, 0.25);
}

/* 变灰状态 */
.main_graph_table_icon_button.disabled .main_graph_table_panel_icon {
    background: linear-gradient(135deg, #9ca3af, #6b7280) !important;
    box-shadow: 0 2px 8px rgba(156, 163, 175, 0.2) !important;
    opacity: 0.6;
}

.main_graph_table_icon_button.disabled .main_graph_table_panel_text {
    color: #9ca3af;
}

.main_graph_table_icon_button.disabled:hover {
    transform: none;
    box-shadow: none;
}

/* 右侧内容区域 */
.main_graph_table_content_area {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 28px;
    min-width: 0;
}

/* 类别行隐藏/显示动画 */
.main_graph_table_category_row.hidden {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
    max-height: 0;
    margin-bottom: 0;
    padding: 0;
    overflow: hidden;
    transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
}

.main_graph_table_category_row.visible {
    opacity: 1;
    transform: translateY(0) scale(1);
    max-height: 500px;
    transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
}

/* 类别行淡入动画 */
@keyframes categoryRowFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 类别行淡出动画 */
@keyframes categoryRowFadeOut {
    from {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    to {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
}

/* 类别行容器 - 丰富的蓝色调设计 */
.main_graph_table_category_row {
    display: flex;
    align-items: center;
    gap: 30px;
    padding: 22px 25px;
    background:
        linear-gradient(135deg,
            rgba(239, 246, 255, 0.95) 0%,
            rgba(219, 234, 254, 0.9) 50%,
            rgba(239, 246, 255, 0.95) 100%
        ),
        radial-gradient(ellipse at top right, rgba(96, 165, 250, 0.08) 0%, transparent 60%);
    border-radius: 16px;
    box-shadow:
        0 6px 20px rgba(59, 130, 246, 0.08),
        0 2px 8px rgba(37, 99, 235, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    border: 1.5px solid rgba(147, 197, 253, 0.4);
    transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
    position: relative;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    overflow: hidden;
}

.main_graph_table_category_row::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(96, 165, 250, 0.1),
        transparent
    );
    transition: all 0.8s ease;
}

.main_graph_table_category_row:hover::before {
    left: 100%;
}

.main_graph_table_category_row:hover {
    background:
        linear-gradient(135deg,
            rgba(255, 255, 255, 0.98) 0%,
            rgba(239, 246, 255, 0.95) 50%,
            rgba(255, 255, 255, 0.98) 100%
        ),
        radial-gradient(ellipse at center, rgba(59, 130, 246, 0.06) 0%, transparent 70%);
    box-shadow:
        0 10px 30px rgba(59, 130, 246, 0.15),
        0 4px 12px rgba(37, 99, 235, 0.08),
        inset 0 2px 0 rgba(255, 255, 255, 0.8);
    border-color: rgba(96, 165, 250, 0.6);
    transform: translateY(-4px) scale(1.01);
}

/* 左侧类别图标标签 - 游戏风格多彩设计 */
.main_graph_table_category_label {
    flex: 0 0 130px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 8px;
    padding: 15px;
    text-align: center;
    position: relative;
    background: transparent;
    border: none;
    color: #475569;
}

.main_graph_table_category_icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 22px;
    color: white;
    margin-bottom: 4px;
    position: relative;
    transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.main_graph_table_category_icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.main_graph_table_category_icon:hover::before {
    opacity: 1;
}

.main_graph_table_category_icon.middleware {
    background: linear-gradient(135deg, #3b82f6, #1e90ff, #25c6fc);
    box-shadow: 0 3px 12px rgba(59, 130, 246, 0.25);
}

.main_graph_table_category_icon.database {
    background: linear-gradient(135deg, #f59e0b, #f97316, #fb923c);
    box-shadow: 0 3px 12px rgba(245, 158, 11, 0.25);
}

.main_graph_table_category_icon.container {
    background: linear-gradient(135deg, #8b5cf6, #a855f7, #c084fc);
    box-shadow: 0 3px 12px rgba(139, 92, 246, 0.25);
}

.main_graph_table_category_icon.backup {
    background: linear-gradient(135deg, #06b6d4, #0891b2, #0e7490);
    box-shadow: 0 3px 12px rgba(6, 182, 212, 0.25);
}

.main_graph_table_category_icon.os {
    background: linear-gradient(135deg, #10b981, #059669, #047857);
    box-shadow: 0 3px 12px rgba(16, 185, 129, 0.25);
}

.main_graph_table_category_icon.cpu {
    background: linear-gradient(135deg, #ef4444, #dc2626, #b91c1c);
    box-shadow: 0 3px 12px rgba(239, 68, 68, 0.25);
}



.main_graph_table_category_text {
    font-size: 13px;
    font-weight: 600;
    color: #64748b;
    letter-spacing: 0.3px;
}

.main_graph_table_category_row:hover .main_graph_table_category_icon {
    transform: scale(1.05);
}

/* 右侧组件容器 */
.main_graph_table_components_container {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: flex-start;
}

/* 组件卡片 - 游戏风格多彩玻璃拟态效果 */
.main_graph_table_component_card {
    position: relative;
    background:
        linear-gradient(135deg,
            rgba(255, 255, 255, 0.3) 0%,
            rgba(255, 255, 255, 0.15) 50%,
            rgba(255, 255, 255, 0.25) 100%
        ),
        radial-gradient(circle at top left, rgba(138, 43, 226, 0.08) 0%, transparent 50%),
        radial-gradient(circle at bottom right, rgba(255, 20, 147, 0.06) 0%, transparent 50%);
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
    border: 1.5px solid rgba(255, 255, 255, 0.4);
    border-radius: 20px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
    min-width: 140px;
    text-align: center;
    overflow: visible;
    box-shadow:
        0 12px 40px rgba(138, 43, 226, 0.12),
        0 4px 16px rgba(255, 20, 147, 0.08),
        0 2px 8px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.5),
        inset 0 -1px 0 rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
    gap: 16px;
    background-clip: padding-box;
}

.main_graph_table_component_card:hover {
    background:
        linear-gradient(135deg,
            rgba(255, 255, 255, 0.35) 0%,
            rgba(255, 255, 255, 0.2) 50%,
            rgba(255, 255, 255, 0.3) 100%
        ),
        radial-gradient(circle at top left, rgba(99, 102, 241, 0.06) 0%, transparent 50%),
        radial-gradient(circle at bottom right, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
    transform: translateY(-2px) scale(1.02);
    box-shadow:
        0 15px 45px rgba(99, 102, 241, 0.15),
        0 6px 20px rgba(139, 92, 246, 0.1),
        0 2px 8px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.6),
        inset 0 -1px 0 rgba(255, 255, 255, 0.3);
}

/* 组件名称 - 稳定的深蓝色风格，无hover效果 */
.main_graph_table_component_name {
    font-size: 17px;
    font-weight: 700;
    color: #1e3a8a;
    margin: 0;
    padding: 14px 18px;
    background:
        linear-gradient(135deg,
            rgba(30, 58, 138, 0.95) 0%,
            rgba(29, 78, 216, 0.9) 50%,
            rgba(30, 58, 138, 0.95) 100%
        );
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border-radius: 14px;
    text-align: center;
    border: 2px solid rgba(30, 58, 138, 0.4);
    position: relative;
    overflow: hidden;
    letter-spacing: 0.4px;
    color: white;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    box-shadow:
        0 6px 20px rgba(30, 58, 138, 0.2),
        0 2px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(30, 58, 138, 0.3);
    transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1); /* 添加平滑过渡效果 */
}

/* 移除组件名称的所有hover和动画效果 */
.main_graph_table_component_name::before {
    display: none; /* 完全移除闪光效果 */
}

.main_graph_table_component_card:hover .main_graph_table_component_name {
    /* 保持背景完全不变，只添加缩放和荧光效果 */
    transform: scale(1.05);
    box-shadow:
        /* 保持原有阴影 */
        0 6px 20px rgba(30, 58, 138, 0.2),
        0 2px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(30, 58, 138, 0.3),
        /* 荧光蓝色高亮效果 */
        0 0 15px rgba(59, 130, 246, 0.8),
        0 0 30px rgba(96, 165, 250, 0.6),
        0 0 45px rgba(147, 197, 253, 0.4);
    text-shadow:
        /* 保持原有文字阴影 */
        0 1px 3px rgba(0, 0, 0, 0.3),
        /* 荧光文字效果 */
        0 0 8px rgba(59, 130, 246, 0.9),
        0 0 16px rgba(96, 165, 250, 0.7);
}

/* 版本标签容器 */
.main_graph_table_version_tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
    align-items: center;
}

/* 版本标签 - 浅蓝色风格，与深蓝色名称形成对比 */
.main_graph_table_version_tag {
    display: inline-flex;
    align-items: center;
    padding: 9px 16px;
    background:
        linear-gradient(135deg,
            rgba(219, 234, 254, 0.9) 0%,
            rgba(191, 219, 254, 0.95) 50%,
            rgba(219, 234, 254, 0.9) 100%
        );
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    color: #2563eb;
    border: 1.5px solid rgba(37, 99, 235, 0.3);
    border-radius: 28px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    min-width: 70px;
    justify-content: center;
    box-shadow:
        0 4px 16px rgba(37, 99, 235, 0.12),
        0 2px 6px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.7),
        inset 0 -1px 0 rgba(37, 99, 235, 0.1);
    letter-spacing: 0.3px;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.main_graph_table_version_tag::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(96, 165, 250, 0.3),
        transparent
    );
    transition: all 0.6s ease;
}

.main_graph_table_version_tag:hover::before {
    left: 100%;
}

.main_graph_table_version_tag:hover {
    transform: translateY(-3px) scale(1.05);
    background:
        linear-gradient(135deg,
            rgba(147, 197, 253, 0.8) 0%,
            rgba(96, 165, 250, 0.85) 50%,
            rgba(147, 197, 253, 0.8) 100%
        );
    border-color: rgba(59, 130, 246, 0.6);
    color: #1e40af;
    box-shadow:
        0 6px 20px rgba(59, 130, 246, 0.2),
        0 3px 8px rgba(37, 99, 235, 0.12),
        inset 0 1px 0 rgba(255, 255, 255, 0.8),
        inset 0 -1px 0 rgba(59, 130, 246, 0.15);
}

.main_graph_table_version_tag.active {
    background: linear-gradient(
        135deg,
        rgba(34, 197, 94, 0.95) 0%,
        rgba(22, 163, 74, 0.9) 50%,
        rgba(34, 197, 94, 0.95) 100%
    );
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
    color: white;
    border-color: rgba(255, 255, 255, 0.4);
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
    box-shadow:
        0 0 30px rgba(34, 197, 94, 0.5),
        0 0 60px rgba(22, 163, 74, 0.3),
        0 6px 25px rgba(34, 197, 94, 0.4),
        inset 0 2px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(34, 197, 94, 0.2);
    transform: translateY(-5px) scale(1.1);
    animation: activeVersionGreenGlow 3s infinite ease-in-out;
}

@keyframes activeVersionGreenGlow {
    0% {
        box-shadow:
            0 0 25px rgba(34, 197, 94, 0.4),
            0 0 50px rgba(22, 163, 74, 0.25),
            0 5px 20px rgba(34, 197, 94, 0.35),
            inset 0 2px 0 rgba(255, 255, 255, 0.3),
            inset 0 -1px 0 rgba(34, 197, 94, 0.2);
    }
    50% {
        box-shadow:
            0 0 35px rgba(34, 197, 94, 0.6),
            0 0 70px rgba(22, 163, 74, 0.4),
            0 8px 30px rgba(34, 197, 94, 0.5),
            inset 0 2px 0 rgba(255, 255, 255, 0.4),
            inset 0 -1px 0 rgba(34, 197, 94, 0.3);
    }
    100% {
        box-shadow:
            0 0 25px rgba(34, 197, 94, 0.4),
            0 0 50px rgba(22, 163, 74, 0.25),
            0 5px 20px rgba(34, 197, 94, 0.35),
            inset 0 2px 0 rgba(255, 255, 255, 0.3),
            inset 0 -1px 0 rgba(34, 197, 94, 0.2);
    }
}

@keyframes glassActiveGlow {
    0% {
        box-shadow:
            0 0 20px rgba(30, 64, 175, 0.3),
            0 0 40px rgba(59, 130, 246, 0.15),
            0 4px 15px rgba(30, 64, 175, 0.25),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }
    50% {
        box-shadow:
            0 0 30px rgba(30, 64, 175, 0.5),
            0 0 60px rgba(59, 130, 246, 0.25),
            0 6px 25px rgba(30, 64, 175, 0.35),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }
    100% {
        box-shadow:
            0 0 20px rgba(30, 64, 175, 0.3),
            0 0 40px rgba(59, 130, 246, 0.15),
            0 4px 15px rgba(30, 64, 175, 0.25),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }
}

/* 组件卡片悬停效果 */
.main_graph_table_component_card:hover {
    transform: translateY(-4px) scale(1.02);
    background: rgba(255, 255, 255, 0.35);
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow:
        0 12px 40px rgba(59, 130, 246, 0.15),
        0 4px 12px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

/* 移除重复的hover样式定义，使用前面定义的荧光效果 */

/* 组件卡片激活状态 - 亮的淡蓝色背景 */
.main_graph_table_component_card.active {
    background:
        linear-gradient(135deg,
            rgba(147, 197, 253, 0.6) 0%,
            rgba(96, 165, 250, 0.55) 50%,
            rgba(147, 197, 253, 0.6) 100%
        );
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border-color: rgba(96, 165, 250, 0.7);
    box-shadow:
        0 4px 15px rgba(96, 165, 250, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transform: translateY(-4px) scale(1.03);
}

/* 移除闪动动画，使用静态亮蓝色背景 */

.main_graph_table_component_card.active .main_graph_table_component_name {
    background: linear-gradient(
        135deg,
        rgba(30, 64, 175, 0.9) 0%,
        rgba(37, 99, 235, 0.95) 50%,
        rgba(59, 130, 246, 0.9) 100%
    );
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow:
        0 6px 20px rgba(30, 64, 175, 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* 删除旧的单版本样式 */
.main_graph_table_version_tag.single-version {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    color: #1e40af;
    border: 1.5px solid rgba(30, 64, 175, 0.3);
}

.main_graph_table_version_tag.single-version:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(37, 99, 235, 0.5);
    color: #1d4ed8;
}

.main_graph_table_version_tag.single-version.active {
    background: linear-gradient(
        135deg,
        rgba(30, 64, 175, 0.9) 0%,
        rgba(37, 99, 235, 0.95) 50%,
        rgba(59, 130, 246, 0.9) 100%
    );
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
}

/* 删除旧的版本指示器和下拉菜单样式 */
.main_graph_table_version_indicator,
.main_graph_table_version_dropdown,
.main_graph_table_version_option {
    display: none !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .main_graph_table_category_row {
        flex-direction: column;
        gap: 15px;
    }

    .main_graph_table_category_label {
        flex: none;
        align-self: flex-start;
        min-width: 100px;
    }

    .main_graph_table_components_container {
        justify-content: flex-start;
    }

    .main_graph_table_component_card {
        min-width: 100%;
        margin-bottom: 10px;
    }

    .main_graph_table_component_name {
        font-size: 14px;
        padding: 6px 10px;
    }

    .main_graph_table_version_tag {
        font-size: 11px;
        padding: 3px 8px;
        min-width: 50px;
    }

    .main_graph_table_version_tags {
        gap: 4px;
    }
}
</style>

<!-- 添加ECharts库 -->
<script src="/static/echarts-5.6.0/dist/echarts.min.js"></script>



<!-- 图示视图容器 -->
<div id="main_graph_table_container" class="main_graph_table_container">
    <!-- 标题栏 -->
     <br>
    <h1 style="font-weight:bold;text-align: center; font-size: 40px; background: linear-gradient(to right, #1a26d3, #4fdfe9); -webkit-background-clip: text; color: transparent;  text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.3);z-index: 1700;">业务支持组件版本管理</h1>
    <br>
    <br>

    <!-- 气泡列展示区域 -->
    <div class="bubble_showcase">
        <div class="bubble_showcase_header">
            <div class="bubble_showcase_title">
                <i class="fas fa-project-diagram bubble_icon_pulse"></i>
                <h2>组件关系图</h2>
            </div>
                <button id="main_graph_table_refresh_btn" class="main_graph_table_refresh">
                        <span class="refresh_icon"><i class="fas fa-sync-alt"></i></span>
                        <span>刷新视图</span>
                </button>
        </div>

        <div class="bubble_description">
            点击下方组件查询关系，系统将自动生成组件关联图谱
        </div>

         <!-- 新的分类组件布局 -->
        <div class="main_graph_table_category_container">
            <!-- 左侧竖向图标显示板 -->
            <div class="main_graph_table_icon_panel">
                <!-- 图标按钮将通过JavaScript动态生成 -->
            </div>

            <!-- 右侧内容区域 -->
            <div class="main_graph_table_content_area">
                <!-- 类别行将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <!-- 思维导图容器 -->
    <div id="main_graph_table_mindmap_container" class="main_graph_table_mindmap_container">
        <div class="main_graph_table_mindmap_header">
            <h3 class="main_graph_table_mindmap_title">组件关联图谱</h3>
            <div class="main_graph_table_mindmap_controls">
                <button id="main_graph_table_close_mindmap_btn" class="main_graph_table_close_mindmap">
                    <i class="fas fa-times"></i>
                </button>
                <button id="main_graph_table_reset_mindmap_btn" class="main_graph_table_reset_mindmap">
                    <i class="fas fa-sync"></i>
                </button>
            </div>
        </div>
        <br>
        <br>
        <br>

        <!-- 添加专门的子区域用于树状图展示 -->
        {% include "components/main_graph_table_tree.html" %}

        <!-- 引入组件关系图结构 -->
        {% include "components/main_graph_table_graph.html" %}

        <!-- 引入组件关系太阳图 -->
        {% include "components/main_graph_table_sunburst.html" %}

        <!--<div class="main_graph_table_mindmap_zoom_controls">
            <button id="main_graph_table_zoom_in_btn" class="main_graph_table_zoom_btn">
                <i class="fas fa-plus"></i>
            </button>
            <button id="main_graph_table_zoom_out_btn" class="main_graph_table_zoom_btn">
                <i class="fas fa-minus"></i>
            </button>
        </div>-->
    </div>

    <!-- 加载动画 -->
    <div id="main_graph_table_loading" class="main_graph_table_loading">
        <div class="main_graph_table_spinner"></div>
    </div>
</div>

<!-- JavaScript功能实现 -->
<script>
// 全局变量
const main_graph_table_categoryContainer = document.querySelector('.main_graph_table_category_container');
const main_graph_table_iconPanel = document.querySelector('.main_graph_table_icon_panel');
const main_graph_table_contentArea = document.querySelector('.main_graph_table_content_area');
const main_graph_table_container = document.getElementById('main_graph_table_container');
const main_graph_table_loading = document.getElementById('main_graph_table_loading');
const main_graph_table_refreshBtn = document.getElementById('main_graph_table_refresh_btn');
const main_graph_table_mindmapContainer = document.getElementById('main_graph_table_mindmap_container');
const main_graph_table_mindmap = document.getElementById('main_graph_table_mindmap');
const main_graph_table_closeMindmapBtn = document.getElementById('main_graph_table_close_mindmap_btn');
const main_graph_table_resetMindmapBtn = document.getElementById('main_graph_table_reset_mindmap_btn');
const main_graph_table_zoomInBtn = document.getElementById('main_graph_table_zoom_in_btn');
const main_graph_table_zoomOutBtn = document.getElementById('main_graph_table_zoom_out_btn');

// ECharts实例
let main_graph_table_echartsInstance = null;
let main_graph_table_currentZoomRatio = 1.0;
// 标记是否正在生成思维导图，防止快速点击引起问题
let main_graph_table_isGeneratingMindmap = false;

// 存储原始数据和筛选状态
let main_graph_table_originalData = [];
let main_graph_table_filteredData = [];
let main_graph_table_activeFilters = {};
let main_graph_table_activeSelections = new Set();
let main_graph_table_lastQuerySuccess = true; // 标记最后一次查询是否成功

// 图标显示板状态管理
let main_graph_table_categoryVisibility = {}; // 记录每个类别的显示状态
let main_graph_table_iconButtons = {}; // 存储图标按钮的引用

// 定义组件类别顺序 - 每个类别独占一行
const main_graph_table_categoryOrder = [
    ['中间件'],     // 第一行：中间件
    ['数据库'],     // 第二行：数据库
    ['容器'],       // 第三行：容器
    ['备份软件'],   // 第四行：备份软件
    ['操作系统'],   // 第五行：操作系统
    ['CPU']        // 第六行：CPU
];

// 定义互斥类别 - 这些类别之间是互斥的，只能有一个被激活
const main_graph_table_exclusiveCategories = ['中间件', '数据库', '容器'];

// 初始化函数 - 页面加载时调用
function main_graph_table_init() {
    // 绑定刷新按钮事件
    main_graph_table_refreshBtn.addEventListener('click', main_graph_table_refreshData);
    main_graph_table_closeMindmapBtn.addEventListener('click', main_graph_table_hideMindmap);
    main_graph_table_resetMindmapBtn.addEventListener('click', main_graph_table_resetMindmap);

    // 安全地添加缩放按钮事件监听器
    if (main_graph_table_zoomInBtn) {
        main_graph_table_zoomInBtn.addEventListener('click', main_graph_table_zoomIn);
    }

    if (main_graph_table_zoomOutBtn) {
        main_graph_table_zoomOutBtn.addEventListener('click', main_graph_table_zoomOut);
    }

    // 添加鼠标滚轮事件，实现横向滚动
    main_graph_table_categoryContainer.addEventListener('wheel', function(event) {
        // 新布局不需要特殊滚动处理，使用默认的竖向滚动
    }, { passive: true });



    // 初始加载数据
    main_graph_table_loadData();

    // 隐藏左侧面板切换按钮
    main_graph_table_hidePanelToggle();
}

// 隐藏左侧面板切换按钮
function main_graph_table_hidePanelToggle() {
    const panelToggle = document.getElementById('panelToggle');
    if (panelToggle) {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                    if (!main_graph_table_container.classList.contains('hidden')) {
                        panelToggle.style.display = 'none';
                    }
                }
            });
        });

        observer.observe(panelToggle, { attributes: true });

        // 立即设置初始状态
        if (!main_graph_table_container.classList.contains('hidden')) {
            panelToggle.style.display = 'none';
        }

        // 监听图示视图容器的可见性变化
        const containerObserver = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    if (main_graph_table_container.classList.contains('hidden')) {
                        panelToggle.style.display = '';
                    } else {
                        panelToggle.style.display = 'none';
                    }
                }
            });
        });

        containerObserver.observe(main_graph_table_container, { attributes: true });
    }
}





// 加载数据函数 - 修改为直接从rms_software_info表获取数据
function main_graph_table_loadData() {
    // 显示加载状态
    main_graph_table_showLoading();

    // 发送AJAX请求获取所有软件信息数据
    $.ajax({
        url: "{{url_for('.get_all_software_info')}}",
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            // 检查是否为空结果响应
            if (!response || response.length === 0) {
                console.log("软件信息查询结果为空");

                // 显示空结果的高级弹窗
                if (typeof showAlert === 'function') {
                    showAlert('无软件信息数据', 'info', '查询结果');
                }

                // 设置空数据
                main_graph_table_originalData = [];
                main_graph_table_filteredData = [];
                main_graph_table_lastQuerySuccess = true;

                // 处理空数据并生成图示
                main_graph_table_processData([]);

                // 隐藏加载状态
                main_graph_table_hideLoading();
                return;
            }

            // 保存原始数据
            main_graph_table_originalData = [...response];
            main_graph_table_filteredData = [...response];
            main_graph_table_lastQuerySuccess = true;

            // 处理数据并生成图示
            main_graph_table_processData(response);

            // 隐藏加载状态
            main_graph_table_hideLoading();
        },
        error: function(error) {
            console.error("加载数据失败:", error);
            main_graph_table_lastQuerySuccess = false;
            main_graph_table_hideLoading();
            showAlert("加载数据失败，请刷新页面重试", "error", "加载失败");
        }
    });
}

// 刷新数据函数
function main_graph_table_refreshData() {
    // 清空筛选条件
    main_graph_table_activeFilters = {};
    main_graph_table_activeSelections.clear();

    // 恢复所有互斥类别的展开状态
    main_graph_table_expandAllExclusiveCategories();

    // 隐藏思维导图
    main_graph_table_hideMindmap();

    // 重新加载数据
    main_graph_table_loadData();
}

// 处理数据并生成图示 - 修改为左侧图标板 + 右侧类别行
function main_graph_table_processData(data) {
    // 清空内容区域
    main_graph_table_contentArea.innerHTML = '';

    // 按类别组织数据
    const categorizedData = main_graph_table_organizeDataByCategory(data);

    // 生成左侧图标显示板
    main_graph_table_generateIconPanel(categorizedData);

    // 按预定义顺序创建类别行
    main_graph_table_categoryOrder.forEach(categoryRow => {
        // 为每一行创建一个行容器
        main_graph_table_createCategoryRowContainer(categoryRow, categorizedData);
    });

    // 处理其他未预定义的类别（如果有的话）
    const allCategories = main_graph_table_categoryOrder.flat();
    Object.keys(categorizedData).forEach(category => {
        if (!allCategories.includes(category) && categorizedData[category].length > 0) {
            // 为未预定义的类别创建单独的行
            main_graph_table_createCategoryRowContainer([category], categorizedData);
        }
    });
}

// 按类别组织数据 - 修改为直接从rms_software_info数据组织
function main_graph_table_organizeDataByCategory(data) {
    const categorizedData = {};

    // 初始化所有预定义类别（扁平化二维数组）
    const allCategories = main_graph_table_categoryOrder.flat();
    allCategories.forEach(category => {
        categorizedData[category] = [];
    });

    // 遍历软件信息数据，按type字段分组
    data.forEach(item => {
        const type = item.type;
        const name = item.name;
        const version = item.version;

        // 检查type是否在预定义的类别中
        if (allCategories.includes(type)) {
            // 查找是否已存在相同名称的组件
            let existingComponent = categorizedData[type].find(comp => comp.name === name);

            if (existingComponent) {
                // 添加版本到现有组件
                if (!existingComponent.versions.includes(version)) {
                    existingComponent.versions.push(version);
                }
            } else {
                // 创建新组件
                categorizedData[type].push({
                    name: name,
                    versions: [version],
                    fullValue: `${name} ${version}`,
                    column: type
                });
            }
        }
    });

    return categorizedData;
}

// 解析组件信息（名称和版本）
function main_graph_table_parseComponentInfo(value) {
    const parts = value.split(' ');
    if (parts.length >= 2) {
        return {
            name: parts[0],
            version: parts.slice(1).join(' ')
        };
    } else {
        return {
            name: value,
            version: '默认版本'
        };
    }
}

// 创建类别行容器 - 修改为添加到右侧内容区域
function main_graph_table_createCategoryRowContainer(categoryRow, categorizedData) {
    // 创建行容器
    const rowContainer = document.createElement('div');
    rowContainer.className = 'main_graph_table_category_row visible';

    // 为行中的每个类别创建类别区域
    categoryRow.forEach(categoryName => {
        const components = categorizedData[categoryName] || [];
        const categorySection = main_graph_table_createCategorySection(categoryName, components);
        rowContainer.appendChild(categorySection);

        // 初始化类别可见性状态
        main_graph_table_categoryVisibility[categoryName] = true;
    });

    main_graph_table_contentArea.appendChild(rowContainer);
}

// 创建类别区域 - 修改原来的createCategoryRow函数
function main_graph_table_createCategorySection(categoryName, components) {
    // 创建类别区域容器
    const categorySection = document.createElement('div');
    categorySection.className = 'main_graph_table_category_section';
    categorySection.dataset.category = categoryName;

    // 创建左侧类别标签
    const categoryLabel = document.createElement('div');
    categoryLabel.className = 'main_graph_table_category_label';

    // 创建图标容器
    const categoryIcon = document.createElement('div');
    categoryIcon.className = 'main_graph_table_category_icon';

    // 创建文字标签
    const categoryText = document.createElement('div');
    categoryText.className = 'main_graph_table_category_text';
    categoryText.textContent = categoryName;

    // 根据类别设置不同的图标和样式
    switch(categoryName) {
        case '中间件':
            categoryIcon.classList.add('middleware');
            categoryIcon.innerHTML = '<i class="fas fa-server"></i>';
            break;
        case '数据库':
            categoryIcon.classList.add('database');
            categoryIcon.innerHTML = '<i class="fas fa-database"></i>';
            break;
        case '容器':
            categoryIcon.classList.add('container');
            categoryIcon.innerHTML = '<i class="fas fa-box"></i>';
            break;
        case '备份软件':
            categoryIcon.classList.add('backup');
            categoryIcon.innerHTML = '<i class="fas fa-save"></i>';
            break;
        case '操作系统':
            categoryIcon.classList.add('os');
            categoryIcon.innerHTML = '<i class="fas fa-desktop"></i>';
            break;
        case 'CPU':
            categoryIcon.classList.add('cpu');
            categoryIcon.innerHTML = '<i class="fas fa-microchip"></i>';
            break;
        default:
            categoryIcon.classList.add('middleware'); // 默认样式
            categoryIcon.innerHTML = '<i class="fas fa-cube"></i>';
    }

    categoryLabel.appendChild(categoryIcon);
    categoryLabel.appendChild(categoryText);

    // 移除复杂的点击事件，保持简单布局

    categorySection.appendChild(categoryLabel);

    // 创建右侧组件容器
    const componentsContainer = document.createElement('div');
    componentsContainer.className = 'main_graph_table_components_container';

    // 为每个组件创建卡片
    if (components && components.length > 0) {
        components.forEach(component => {
            const componentCard = main_graph_table_createComponentCard(component);
            componentsContainer.appendChild(componentCard);
        });
    } else {
        // 如果没有组件，显示空状态提示
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'main_graph_table_empty_message';
        emptyMessage.textContent = '暂无组件';
        emptyMessage.style.cssText = `
            color: #9ca3af;
            font-style: italic;
            padding: 20px;
            text-align: center;
            font-size: 14px;
        `;
        componentsContainer.appendChild(emptyMessage);
    }

    categorySection.appendChild(componentsContainer);
    return categorySection;
}

// 创建组件卡片
function main_graph_table_createComponentCard(component) {
    // 创建卡片容器
    const card = document.createElement('div');
    card.className = 'main_graph_table_component_card';
    card.dataset.column = component.column;
    card.dataset.name = component.name;

    // 创建组件名称
    const nameElement = document.createElement('div');
    nameElement.className = 'main_graph_table_component_name';
    nameElement.textContent = component.name;
    card.appendChild(nameElement);

    // 创建版本标签容器
    const versionTagsContainer = document.createElement('div');
    versionTagsContainer.className = 'main_graph_table_version_tags';

    // 为每个版本创建标签
    component.versions.forEach(version => {
        const versionTag = document.createElement('div');
        versionTag.className = 'main_graph_table_version_tag';

        versionTag.textContent = version;
        versionTag.dataset.column = component.column;
        versionTag.dataset.value = `${component.name} ${version}`;

        // 绑定点击事件到版本标签
        versionTag.addEventListener('click', function(event) {
            event.stopPropagation();
            main_graph_table_handleComponentClick(this, event);
        });

        // 添加波纹效果
        versionTag.addEventListener('click', function(event) {
            main_graph_table_createRippleEffect(this, event);
        });

        // 阻止版本标签的hover事件冒泡到父卡片
        versionTag.addEventListener('mouseenter', function(event) {
            event.stopPropagation();
        });

        versionTag.addEventListener('mouseleave', function(event) {
            event.stopPropagation();
        });

        versionTagsContainer.appendChild(versionTag);
    });

    card.appendChild(versionTagsContainer);

    return card;
}

// 处理组件点击事件
function main_graph_table_handleComponentClick(element, event) {
    // 获取组件数据
    const column = element.dataset.column;
    const value = element.dataset.value;
    const key = `${column}:${value}`;

    // 重置所有组件选择
    main_graph_table_activeSelections.clear();
    main_graph_table_activeFilters = {};

    // 清除所有活跃状态
    document.querySelectorAll('.main_graph_table_component_card.active').forEach(el => {
        el.classList.remove('active');
    });
    document.querySelectorAll('.main_graph_table_version_tag.active').forEach(el => {
        el.classList.remove('active');
    });

    // 处理互斥类别逻辑
    if (main_graph_table_exclusiveCategories.includes(column)) {
        // 如果点击的是互斥类别，需要收回其他互斥类别的卡片
        main_graph_table_collapseOtherExclusiveCategories(column);
    }

    // 选中当前版本标签
    if (element.classList.contains('main_graph_table_version_tag')) {
        element.classList.add('active');
        // 同时激活父卡片
        const parentCard = element.closest('.main_graph_table_component_card');
        if (parentCard) {
            parentCard.classList.add('active');
        }
    }

    main_graph_table_activeSelections.add(key);
    main_graph_table_activeFilters[column] = value;

    // 应用筛选
    main_graph_table_applyFilters();
}

// 收回其他互斥类别的卡片 - 修改为上移到同一行，并动态调整高度
function main_graph_table_collapseOtherExclusiveCategories(activeCategory) {
    // 找到激活类别所在的行
    const activeCategorySection = document.querySelector(`[data-category="${activeCategory}"]`);
    if (!activeCategorySection) return;

    const activeRow = activeCategorySection.closest('.main_graph_table_category_row');
    if (!activeRow) return;

    // 获取激活类别的高度作为参考
    const activeRowHeight = activeRow.offsetHeight;

    // 收集其他互斥类别的区域
    const otherCategories = main_graph_table_exclusiveCategories.filter(cat => cat !== activeCategory);
    const otherCategorySections = [];

    otherCategories.forEach(category => {
        const categorySection = document.querySelector(`[data-category="${category}"]`);
        if (categorySection) {
            // 从原来的行中移除
            const originalRow = categorySection.closest('.main_graph_table_category_row');
            if (originalRow && originalRow !== activeRow) {
                categorySection.remove();

                // 如果原来的行变空了，隐藏它
                if (originalRow.children.length === 0) {
                    originalRow.style.display = 'none';
                    originalRow.dataset.wasHidden = 'true';
                }
            }

            // 修改类别区域的样式，使其更紧凑
            categorySection.classList.add('collapsed-to-row');

            // 动态调整高度以匹配激活类别所在行的高度
            categorySection.style.height = `${activeRowHeight - 16}px`; // 减去一些padding
            categorySection.style.minHeight = `${activeRowHeight - 16}px`;
            categorySection.style.maxHeight = `${activeRowHeight - 16}px`;
            categorySection.style.display = 'flex';
            categorySection.style.alignItems = 'center';
            categorySection.style.justifyContent = 'center';

            // 隐藏组件容器，只显示类别标签
            const componentsContainer = categorySection.querySelector('.main_graph_table_components_container');
            if (componentsContainer) {
                componentsContainer.style.display = 'none';
            }

            // 添加点击事件到类别标签，允许重新展开
            const categoryLabel = categorySection.querySelector('.main_graph_table_category_label');
            if (categoryLabel) {
                categoryLabel.style.cursor = 'pointer';
                categoryLabel.title = '点击展开此类别';

                // 移除之前的点击事件监听器
                categoryLabel.replaceWith(categoryLabel.cloneNode(true));
                const newCategoryLabel = categorySection.querySelector('.main_graph_table_category_label');

                newCategoryLabel.addEventListener('click', function() {
                    main_graph_table_expandCategory(category);
                });
            }

            otherCategorySections.push(categorySection);
        }
    });

    // 将其他类别添加到激活类别所在的行
    otherCategorySections.forEach(section => {
        activeRow.appendChild(section);
    });

    // 微调激活行的布局以适应新的紧凑元素
    activeRow.style.alignItems = 'center';
    activeRow.style.minHeight = `${activeRowHeight}px`;
}

// 展开指定类别的卡片 - 修改为恢复原来的布局
function main_graph_table_expandCategory(category) {
    // 恢复所有互斥类别到原来的布局
    main_graph_table_restoreOriginalLayout();
}

// 展开所有互斥类别的卡片
function main_graph_table_expandAllExclusiveCategories() {
    // 显示所有类别
    const allCategories = main_graph_table_categoryOrder.flat();
    allCategories.forEach(categoryName => {
        main_graph_table_showCategoryRow(categoryName);
    });
}

// 恢复原始布局
function main_graph_table_restoreOriginalLayout() {
    // 重新生成整个布局
    const currentData = main_graph_table_originalData.length > 0 ? main_graph_table_originalData : main_graph_table_filteredData;
    main_graph_table_processData(currentData);
}

// 应用筛选，获取符合条件的数据
function main_graph_table_applyFilters() {
    // 如果没有活跃过滤器，使用原始数据
    if (Object.keys(main_graph_table_activeFilters).length === 0) {
        main_graph_table_filteredData = [...main_graph_table_originalData];

        // 隐藏思维导图
        main_graph_table_hideMindmap();
        return;
    }

    // 准备查询参数
    const queryData = [];

    // 转换筛选条件为后端查询格式
    Object.entries(main_graph_table_activeFilters).forEach(([column, value]) => {
        // 提取值的组件类型和名称（假设格式为"类型 名称 版本"）
        const parts = value.split(' ');
        let type, name, version;

        if (column === '中间件' || column === '用途' || column === '地点' || column === '数据库') {
            // 如果列名是预定义的类型，直接使用
            type = column;

            if (parts.length >= 2) {
                name = parts[0];
                version = parts.slice(1).join(' ');
            } else {
                name = value;
                version = '全部';
            }
        } else {
            // 使用更通用的方法解析
            if (parts.length >= 2) {
                type = column;
                name = parts[0];
                version = parts.slice(1).join(' ');
            } else {
                type = column;
                name = value;
                version = '全部';
            }
        }

        queryData.push({
            "type": type,
            "name": name,
            "version": version
        });
    });

    // 如果没有构建有效的查询条件，使用原始数据
    if (queryData.length === 0) {
        main_graph_table_filteredData = [...main_graph_table_originalData];
        return;
    }

    // 显示加载状态
    main_graph_table_showLoading();

    // 发送查询请求
    $.ajax({
        url: "{{url_for('.software_check_start')}}",
        type: 'POST',
        data: JSON.stringify(queryData),
        contentType: 'application/json',
        dataType: 'json',
        success: function(response) {
            // 标记查询成功
            main_graph_table_lastQuerySuccess = true;

            // 更新过滤后的数据
            main_graph_table_filteredData = [...response];

            // 更新表格数据（如果表格视图相关函数存在）
            if (typeof button_search === 'function') {
                // 传递查询参数给表格视图的查询函数
                button_search(queryData);
            }

            // 高亮关联项
            main_graph_table_highlightRelatedItems();

            // 生成思维导图
            main_graph_table_generateMindmap();

            // 平滑滚动到思维导图
            setTimeout(() => {
                if (main_graph_table_mindmapContainer.classList.contains('active')) {
                    main_graph_table_mindmapContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            }, 600);

            // 隐藏加载状态
            main_graph_table_hideLoading();
        },
        error: function(error) {
            console.error("查询失败:", error);

            // 标记查询失败
            main_graph_table_lastQuerySuccess = false;

            // 隐藏思维导图
            main_graph_table_hideMindmap();

            main_graph_table_hideLoading();
            showAlert("无匹配内容", "error", "查询失败");
        }
    });
}

// 高亮关联项
function main_graph_table_highlightRelatedItems() {
    // 首先，恢复所有组件和版本标签的默认状态（除了已选中的）
    document.querySelectorAll('.main_graph_table_component_card').forEach(card => {
        const cardColumn = card.dataset.column;
        const cardName = card.dataset.name;

        // 检查这个卡片中是否有选中的版本标签
        let hasSelectedVersion = false;
        const versionTags = card.querySelectorAll('.main_graph_table_version_tag');

        versionTags.forEach(tag => {
            const tagValue = tag.dataset.value;
            const tagKey = `${cardColumn}:${tagValue}`;

            if (main_graph_table_activeSelections.has(tagKey)) {
                hasSelectedVersion = true;
                tag.classList.add('active');
            } else {
                tag.classList.remove('active');
            }
        });

        // 如果卡片中有选中的版本，保持卡片激活状态
        if (hasSelectedVersion) {
            card.classList.add('active');
        } else {
            card.classList.remove('active');
        }
    });

    // 如果没有筛选后的数据，或者上次查询失败，返回
    if (main_graph_table_filteredData.length === 0 || !main_graph_table_lastQuerySuccess) return;

    // 遍历筛选后的数据，高亮相关项
    main_graph_table_filteredData.forEach(item => {
        Object.entries(item).forEach(([column, value]) => {
            // 跳过id列和已选择的列
            if (column === 'id' || main_graph_table_activeFilters[column]) return;

            // 只处理预定义的类别
            const allCategories = main_graph_table_categoryOrder.flat();
            if (!allCategories.includes(column)) return;

            // 查找并高亮相关版本标签
            const relatedVersionTag = document.querySelector(`.main_graph_table_version_tag[data-column="${column}"][data-value="${value}"]`);
            if (relatedVersionTag) {
                relatedVersionTag.classList.add('active');

                // 同时激活父卡片
                const parentCard = relatedVersionTag.closest('.main_graph_table_component_card');
                if (parentCard) {
                    parentCard.classList.add('active');
                }
            }
        });
    });
}

// 生成思维导图
function main_graph_table_generateMindmap() {
    try {
        // 如果当前正在生成图表，则中止操作
        if (main_graph_table_isGeneratingMindmap) {
            console.warn('上一个思维导图生成操作尚未完成，跳过当前操作');
            return;
        }

        // 标记正在生成图表
        main_graph_table_isGeneratingMindmap = true;

        // 如果没有筛选条件或者数据为空，隐藏思维导图
        if (Object.keys(main_graph_table_activeFilters).length === 0 || !Array.isArray(main_graph_table_filteredData) || main_graph_table_filteredData.length === 0) {
            main_graph_table_hideMindmap();
            main_graph_table_isGeneratingMindmap = false;
            return;
        }

        // 显示思维导图容器和树状图专区
        main_graph_table_mindmapContainer.classList.add('active');

        // 获取并激活树状图专区
        const treeShowcase = document.querySelector('.main_graph_table_tree_showcase');
        if (treeShowcase) {
            treeShowcase.classList.add('active');
        }

        // 获取并激活图关系图专区
        const graphShowcase = document.querySelector('.main_graph_table_graph_showcase');
        if (graphShowcase) {
            graphShowcase.classList.add('active');
        }

        // 获取并激活太阳图专区
        const sunburstShowcase = document.querySelector('.main_graph_table_sunburst_showcase');
        if (sunburstShowcase) {
            sunburstShowcase.classList.add('active');
        }

        // 安全地销毁可能存在的旧实例
        if (main_graph_table_echartsInstance) {
            try {
                const chartContainer = document.getElementById('main_graph_table_echarts_container');
                if (chartContainer && chartContainer.getAttribute('_echarts_instance_')) {
                    main_graph_table_echartsInstance.dispose();
                }
            } catch (disposeError) {
                console.warn('销毁旧的ECharts实例时出错:', disposeError);
            }
            main_graph_table_echartsInstance = null;
        }

        // 获取根节点信息（选中的气泡）
        const rootColumn = Object.keys(main_graph_table_activeFilters)[0];
        const rootValue = main_graph_table_activeFilters[rootColumn];

        if (!rootColumn || !rootValue) {
            console.error('无法获取有效的根节点信息');
            main_graph_table_hideMindmap();
            main_graph_table_isGeneratingMindmap = false;
            return;
        }

        // 使用延迟初始化树结构图，等待DOM完全更新
        setTimeout(() => {
            // 初始化树结构图 - 使用外部组件提供的接口函数
            if (typeof main_graph_table_tree_init === 'function') {
                main_graph_table_tree_init(rootColumn, rootValue, main_graph_table_filteredData);
            }

            // 初始化关系图
            if (typeof main_graph_table_initRelationGraph === 'function') {
                main_graph_table_initRelationGraph(rootColumn, rootValue, main_graph_table_filteredData);
            }

            // 初始化太阳图
            if (typeof main_graph_table_initSunburst === 'function') {
                main_graph_table_initSunburst(rootColumn, rootValue, main_graph_table_filteredData);
            }

            // 添加缩放事件
            if (main_graph_table_zoomInBtn) {
                main_graph_table_zoomInBtn.onclick = function() {
                    main_graph_table_zoomIn();
                };
            }

            if (main_graph_table_zoomOutBtn) {
                main_graph_table_zoomOutBtn.onclick = function() {
                    main_graph_table_zoomOut();
                };
            }

            // 平滑滚动到思维导图
            setTimeout(() => {
                if (main_graph_table_mindmapContainer.classList.contains('active')) {
                    main_graph_table_mindmapContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
                // 标记生成完成
                main_graph_table_isGeneratingMindmap = false;
            }, 600);
        }, 300);

    } catch (error) {
        console.error('生成思维导图时出错:', error);
        main_graph_table_hideMindmap();
        main_graph_table_isGeneratingMindmap = false;
        showAlert('加载组件关联图谱失败，请重试或联系管理员', 'error', '图谱加载失败');
    }
}

// 调整Echarts实例大小
function main_graph_table_resizeEcharts() {
    if (main_graph_table_echartsInstance) {
        try {
            main_graph_table_echartsInstance.resize();
        } catch (error) {
            console.warn('调整ECharts实例大小时出错:', error);
        }
    }
}

// 添加窗口大小变化监听
window.addEventListener('resize', main_graph_table_resizeEcharts);

// 显示加载状态
function main_graph_table_showLoading() {
    // 使用用户定义的全局加载函数
    if (typeof showLoading === 'function') {
        showLoading();
    } else {
        // 使用默认加载方式
        main_graph_table_loading.classList.add('active');
    }
}

// 隐藏加载状态
function main_graph_table_hideLoading() {
    // 使用用户定义的全局加载函数
    if (typeof hideLoading === 'function') {
        hideLoading();
    } else {
        // 使用默认加载方式
        main_graph_table_loading.classList.remove('active');
    }
}

// 重置思维导图
function main_graph_table_resetMindmap() {
    // 清空筛选条件
    main_graph_table_activeFilters = {};
    main_graph_table_activeSelections.clear();

    // 恢复所有互斥类别的展开状态
    main_graph_table_expandAllExclusiveCategories();

    // 隐藏思维导图
    main_graph_table_hideMindmap();

    // 重新加载数据
    main_graph_table_loadData();
}

// 放大思维导图
function main_graph_table_zoomIn() {
    try {
        if (!main_graph_table_echartsInstance) return;

        // 增加缩放比例
        main_graph_table_currentZoomRatio = Math.min(main_graph_table_currentZoomRatio * 1.2, 3);

        // 获取当前选项
        const option = main_graph_table_echartsInstance.getOption();

        if (option && option.series && option.series[0]) {
            // 直接调用缩放方法
            main_graph_table_echartsInstance.dispatchAction({
                type: 'treeZoom',
                seriesIndex: 0,
                zoom: main_graph_table_currentZoomRatio
            });
        }
    } catch (error) {
        console.error('缩放图表时出错:', error);
    }
}

// 缩小思维导图
function main_graph_table_zoomOut() {
    try {
        if (!main_graph_table_echartsInstance) return;

        // 减小缩放比例
        main_graph_table_currentZoomRatio = Math.max(main_graph_table_currentZoomRatio * 0.8, 0.5);

        // 获取当前选项
        const option = main_graph_table_echartsInstance.getOption();

        if (option && option.series && option.series[0]) {
            // 直接调用缩放方法
            main_graph_table_echartsInstance.dispatchAction({
                type: 'treeZoom',
                seriesIndex: 0,
                zoom: main_graph_table_currentZoomRatio
            });
        }
    } catch (error) {
        console.error('缩放图表时出错:', error);
    }
}

// 隐藏思维导图
function main_graph_table_hideMindmap() {
    try {
        // 先移除active类，防止在动画过程中移除DOM元素
        main_graph_table_mindmapContainer.classList.remove('active');

        // 也同时移除树状图专区的active类
        const treeShowcase = document.querySelector('.main_graph_table_tree_showcase');
        if (treeShowcase) {
            treeShowcase.classList.remove('active');
        }

        // 移除图关系图专区的active类
        const graphShowcase = document.querySelector('.main_graph_table_graph_showcase');
        if (graphShowcase) {
            graphShowcase.classList.remove('active');
        }

        // 移除太阳图专区的active类
        const sunburstShowcase = document.querySelector('.main_graph_table_sunburst_showcase');
        if (sunburstShowcase) {
            sunburstShowcase.classList.remove('active');
        }

        // 安全地销毁ECharts实例
        if (main_graph_table_echartsInstance) {
            try {
                const dom = document.getElementById('main_graph_table_echarts_container');
                // 只在DOM元素存在时尝试销毁实例
                if (dom && dom.getAttribute('_echarts_instance_')) {
                    main_graph_table_echartsInstance.dispose();
                } else {
                    // 如果DOM元素不存在，只需要置空实例引用
                    console.log('ECharts容器已被移除，跳过dispose');
                }
            } catch (disposeError) {
                console.warn('销毁ECharts实例时出错:', disposeError);
            }
            main_graph_table_echartsInstance = null;
        }

        // 销毁关系图实例
        if (typeof main_graph_table_graphInstance !== 'undefined' && main_graph_table_graphInstance) {
            try {
                main_graph_table_graphInstance.dispose();
            } catch (disposeError) {
                console.warn('销毁关系图实例时出错:', disposeError);
            }
            main_graph_table_graphInstance = null;
        }

        // 销毁太阳图实例
        if (typeof main_graph_table_sunburstInstance !== 'undefined' && main_graph_table_sunburstInstance) {
            try {
                main_graph_table_sunburstInstance.dispose();
            } catch (disposeError) {
                console.warn('销毁太阳图实例时出错:', disposeError);
            }
            main_graph_table_sunburstInstance = null;
        }
    } catch (error) {
        console.error('隐藏思维导图时出错:', error);
    }
}

// 创建波纹效果
function main_graph_table_createRippleEffect(element, event) {
    // 创建波纹元素
    const ripple = document.createElement('span');
    ripple.className = 'main_graph_table_ripple';

    // 计算点击位置相对于元素的坐标
    const rect = element.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // 设置波纹位置
    ripple.style.left = `${x}px`;
    ripple.style.top = `${y}px`;

    // 添加到元素
    element.appendChild(ripple);

    // 动画结束后移除波纹
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// 生成左侧图标显示板
function main_graph_table_generateIconPanel(categorizedData) {
    // 清空图标显示板
    main_graph_table_iconPanel.innerHTML = '';
    main_graph_table_iconButtons = {};

    // 获取所有类别
    const allCategories = main_graph_table_categoryOrder.flat();

    // 为每个类别创建图标按钮
    allCategories.forEach(categoryName => {
        const iconButton = main_graph_table_createIconButton(categoryName, categorizedData[categoryName] || []);
        main_graph_table_iconPanel.appendChild(iconButton);
        main_graph_table_iconButtons[categoryName] = iconButton;
    });
}

// 创建图标按钮
function main_graph_table_createIconButton(categoryName, components) {
    const button = document.createElement('button');
    button.className = 'main_graph_table_icon_button';
    button.dataset.category = categoryName;

    // 创建图标容器
    const iconDiv = document.createElement('div');
    iconDiv.className = 'main_graph_table_panel_icon';

    // 创建文字标签
    const textDiv = document.createElement('div');
    textDiv.className = 'main_graph_table_panel_text';
    textDiv.textContent = categoryName;

    // 根据类别设置不同的图标和样式
    switch(categoryName) {
        case '中间件':
            iconDiv.classList.add('middleware');
            iconDiv.innerHTML = '<i class="fas fa-server"></i>';
            break;
        case '数据库':
            iconDiv.classList.add('database');
            iconDiv.innerHTML = '<i class="fas fa-database"></i>';
            break;
        case '容器':
            iconDiv.classList.add('container');
            iconDiv.innerHTML = '<i class="fas fa-box"></i>';
            break;
        case '备份软件':
            iconDiv.classList.add('backup');
            iconDiv.innerHTML = '<i class="fas fa-shield-alt"></i>';
            break;
        case '操作系统':
            iconDiv.classList.add('os');
            iconDiv.innerHTML = '<i class="fas fa-desktop"></i>';
            break;
        case 'CPU':
            iconDiv.classList.add('cpu');
            iconDiv.innerHTML = '<i class="fas fa-microchip"></i>';
            break;
        default:
            iconDiv.classList.add('middleware');
            iconDiv.innerHTML = '<i class="fas fa-cog"></i>';
            break;
    }

    // 添加点击事件
    button.addEventListener('click', () => {
        main_graph_table_toggleCategoryVisibility(categoryName);
    });

    // 添加工具提示
    button.title = `点击切换${categoryName}显示状态`;

    button.appendChild(iconDiv);
    button.appendChild(textDiv);

    return button;
}

// 切换类别显示状态
function main_graph_table_toggleCategoryVisibility(categoryName) {
    const isVisible = main_graph_table_categoryVisibility[categoryName];

    // 如果是互斥类别，需要特殊处理
    if (main_graph_table_exclusiveCategories.includes(categoryName)) {
        if (isVisible) {
            // 隐藏当前类别
            main_graph_table_hideCategoryRow(categoryName);
        } else {
            // 显示当前类别，隐藏其他互斥类别
            main_graph_table_exclusiveCategories.forEach(exclusiveCategory => {
                if (exclusiveCategory !== categoryName) {
                    main_graph_table_hideCategoryRow(exclusiveCategory);
                }
            });
            main_graph_table_showCategoryRow(categoryName);
        }
    } else {
        // 非互斥类别，直接切换显示状态
        if (isVisible) {
            main_graph_table_hideCategoryRow(categoryName);
        } else {
            main_graph_table_showCategoryRow(categoryName);
        }
    }
}

// 隐藏类别行
function main_graph_table_hideCategoryRow(categoryName) {
    const categoryRows = main_graph_table_contentArea.querySelectorAll('.main_graph_table_category_row');

    categoryRows.forEach(row => {
        const categorySection = row.querySelector(`[data-category="${categoryName}"]`);
        if (categorySection) {
            row.classList.remove('visible');
            row.classList.add('hidden');

            // 更新状态
            main_graph_table_categoryVisibility[categoryName] = false;

            // 更新图标按钮状态
            const iconButton = main_graph_table_iconButtons[categoryName];
            if (iconButton) {
                iconButton.classList.add('disabled');
            }
        }
    });
}

// 显示类别行
function main_graph_table_showCategoryRow(categoryName) {
    const categoryRows = main_graph_table_contentArea.querySelectorAll('.main_graph_table_category_row');

    categoryRows.forEach(row => {
        const categorySection = row.querySelector(`[data-category="${categoryName}"]`);
        if (categorySection) {
            row.classList.remove('hidden');
            row.classList.add('visible');

            // 更新状态
            main_graph_table_categoryVisibility[categoryName] = true;

            // 更新图标按钮状态
            const iconButton = main_graph_table_iconButtons[categoryName];
            if (iconButton) {
                iconButton.classList.remove('disabled');
            }
        }
    });
}

// 页面加载时初始化图示视图
document.addEventListener('DOMContentLoaded', function() {
    // 初始化图示视图
    main_graph_table_init();
});
</script>

